
import { SimulationStep, LogEntry } from './types';

export const INITIAL_LOGS: LogEntry[] = [
  { timestamp: new Date(), message: '模拟已准备就绪。按“开始”按钮启动。', type: 'info' },
];

export const SIMULATION_STEPS: SimulationStep[] = [
  {
    id: 'app_launch',
    title: '1. 应用启动与窗口创建',
    description: 'Swift 应用启动，主窗口 (NSWindow) 被创建并显示在屏幕上。',
    codeSnippet: `
// AppDelegate.swift
class AppDelegate: NSObject, NSApplicationDelegate {
    var window: NSWindow!

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Create the window
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered, defer: false)
        window.center()
        window.title = "Metal 3D App"
        // ... setup Metal view ...
        window.makeKeyAndOrderFront(nil)
    }
}
    `,
    details: [
        "初始化 NSApplication。",
        "创建 NSWindow 实例。",
        "设置窗口属性（大小、标题、样式）。",
        "使窗口成为关键窗口并显示。"
    ]
  },
  {
    id: 'metal_view_setup',
    title: '2. Metal 视图设置 (MTKView)',
    description: '一个 MTKView 被初始化并添加到窗口中。此视图将处理 Metal 渲染。',
    codeSnippet: `
// ViewController.swift (or within AppDelegate)
import MetalKit

class ViewController: NSViewController {
    var mtkView: MTKView!

    override func viewDidLoad() {
        super.viewDidLoad()
        
        guard let defaultDevice = MTLCreateSystemDefaultDevice() else {
            fatalError("Metal is not supported on this device")
        }
        
        mtkView = MTKView(frame: self.view.bounds, device: defaultDevice)
        mtkView.autoresizingMask = [.width, .height]
        self.view.addSubview(mtkView)
        
        // ... setup renderer ...
    }
}
    `,
    details: [
        "获取默认 MTLDevice。",
        "使用设备和帧初始化 MTKView。",
        "将 MTKView 作为子视图添加到窗口的内容视图中。",
        "配置 MTKView 属性（例如 colorPixelFormat, depthStencilPixelFormat）。"
    ]
  },
  {
    id: 'renderer_init',
    title: '3. 渲染器初始化',
    description: '创建自定义 Renderer 类。该类封装了 Metal 对象，如命令队列、管线状态和缓冲区。',
    codeSnippet: `
// Renderer.swift
import MetalKit

class Renderer: NSObject, MTKViewDelegate {
    let device: MTLDevice
    let commandQueue: MTLCommandQueue
    var pipelineState: MTLRenderPipelineState!
    // ... other Metal objects like vertex buffers, textures ...

    init?(mtkView: MTKView) {
        self.device = mtkView.device!
        self.commandQueue = self.device.makeCommandQueue()!
        super.init()
        setupPipeline()
        // ... load assets ...
    }

    func setupPipeline() { /* ... Compile shaders, create pipeline state ... */ }
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {}
    func draw(in view: MTKView) { /* ... Render commands ... */ }
}

// In ViewController:
// self.renderer = Renderer(mtkView: mtkView)
// mtkView.delegate = self.renderer
    `,
    details: [
        "Renderer 类遵循 MTKViewDelegate 协议。",
        "初始化 MTLCommandQueue。",
        "实现 setupPipeline()以创建 MTLRenderPipelineState（编译着色器）。",
        "加载必要的顶点数据、纹理。",
        "将渲染器设置为 MTKView 的代理。"
    ]
  },
  {
    id: 'cvdisplaylink_init',
    title: '4. CVDisplayLink 初始化',
    description: '创建 CVDisplayLink。此对象将绘图与显示器的刷新率同步。',
    codeSnippet: `
// Renderer.swift or a dedicated DisplayLinkController
import CoreVideo

class Renderer {
    // ... existing properties ...
    var displayLink: CVDisplayLink?

    func setupDisplayLink() {
        CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        // For MTKView, this is often handled internally by setting isPaused = false
        // For custom CAMetalLayer, CVDisplayLink is more common.
        // Here we simulate explicit setup for clarity.
        
        // Set the callback function
        // CVDisplayLinkSetOutputCallback(displayLink!, displayLinkCallback, Unmanaged.passUnretained(self).toOpaque())
    }
}

// CVDisplayLink callback (global or static function)
// func displayLinkCallback(...) -> CVReturn { ... }
    `,
    details: [
        "使用 CVDisplayLinkCreateWithActiveCGDisplays 创建 CVDisplayLink。",
        "注意：MTKView 可以隐式管理其自身的显示链接。此模拟出于教学目的显示了显式的 CVDisplayLink 设置，这在自定义 CAMetalLayer 渲染中很常见。"
    ]
  },
  {
    id: 'cvdisplaylink_callback',
    title: '5. CVDisplayLink 回调设置',
    description: '为 CVDisplayLink 设置回调函数。此函数将为每一帧调用。',
    codeSnippet: `
// Renderer.swift
// CVDisplayLink callback function (typically a C-style function or static method)
let displayLinkCallback: CVDisplayLinkOutputCallback = { 
    (displayLink, now, outputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
    
    // Safely unwrap and call the instance method
    if let context = displayLinkContext {
        let renderer = Unmanaged<Renderer>.fromOpaque(context).takeUnretainedValue()
        // It's crucial to dispatch to the main thread for UI updates or Metal commands
        // if the callback isn't already on the main thread.
        DispatchQueue.main.async {
            renderer.updateAndRender() 
        }
    }
    return kCVReturnSuccess
}

// Inside Renderer.setupDisplayLink()
// CVDisplayLinkSetOutputCallback(displayLink!, displayLinkCallback, Unmanaged.passUnretained(self).toOopaque())
// The 'self' (renderer instance) is passed as context.
    `,
    details: [
        "定义一个 CVDisplayLinkOutputCallback 函数。",
        "此函数接收帧的计时信息。",
        "使用 CVDisplayLinkSetOutputCallback 关联函数和上下文（例如，渲染器实例）。",
        "回调触发主渲染逻辑。"
    ]
  },
  {
    id: 'cvdisplaylink_start',
    title: '6. CVDisplayLink 启动',
    description: 'CVDisplayLink 启动，并开始以显示刷新率调用回调函数。',
    codeSnippet: `
// Renderer.swift
// func startAnimation() {
//    if let displayLink = displayLink {
//        CVDisplayLinkStart(displayLink)
//    }
// }

// For MTKView, this is simpler:
// mtkView.isPaused = false // This starts MTKView's internal display link.
    `,
    details: [
        "在初始化的显示链接上调用 CVDisplayLinkStart()。",
        "或者，对于 MTKView，设置 \`mtkView.isPaused = false\` 会启动其渲染循环。"
    ]
  },
  {
    id: 'render_loop',
    title: '7. 渲染循环 (CVDisplayLink 触发)',
    description: 'CVDisplayLink 回调持续触发，驱动动画。每次调用都会执行渲染周期。',
    isRenderLoop: true,
    codeSnippet: `
// Renderer.swift - updateAndRender() method called by CVDisplayLink callback,
// or draw(in view: MTKView) if using MTKView delegate.

func updateAndRender() { // Or func draw(in view: MTKView) for MTKView
    // 1. Update animation state (e.g., rotation, position)
    //    self.rotationAngle += 0.01 

    guard let commandBuffer = commandQueue.makeCommandBuffer() else { return }
    commandBuffer.label = "MyCommandBuffer"

    // For MTKView:
    // guard let renderPassDescriptor = view.currentRenderPassDescriptor else { return }
    // For CAMetalLayer:
    // guard let drawable = metalLayer.nextDrawable() else { return }
    // let renderPassDescriptor = MTLRenderPassDescriptor()
    // renderPassDescriptor.colorAttachments[0].texture = drawable.texture
    // ... configure renderPassDescriptor ...

    guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else { return }
    renderEncoder.label = "MyRenderEncoder"

    // 2. Set pipeline state, buffers
    renderEncoder.setRenderPipelineState(pipelineState)
    // renderEncoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
    // renderEncoder.setVertexBytes(&uniforms, length: MemoryLayout<Uniforms>.size, index: 1)

    // 3. Issue Draw Calls
    // renderEncoder.drawPrimitives(type: .triangle, vertexStart: 0, vertexCount: 3)
    
    renderEncoder.endEncoding()

    // 4. Present Drawable (for CAMetalLayer)
    // commandBuffer.present(drawable)
    // For MTKView, currentDrawable is automatically presented on commit if it exists.

    // 5. Commit Command Buffer
    commandBuffer.commit()
    // commandBuffer.waitUntilCompleted() // Optional: for synchronization
}
    `,
    details: [
        "CVDisplayLink 回调触发：标志一个新帧。",
        "更新动画状态：根据时间或帧计数修改变换、颜色等。",
        "获取 Drawable 和命令缓冲区：从 CAMetalLayer 获取一个 drawable（或使用 MTKView 的 currentDrawable）并创建一个 MTLCommandBuffer。",
        "创建渲染通道描述符和编码器：配置渲染将如何发生（清除颜色、加载/存储操作）并创建一个 MTLRenderCommandEncoder。",
        "设置管线和缓冲区：告诉 GPU 使用哪些着色器并提供顶点/统一数据。",
        "绘制调用：指示 GPU 渲染图元。",
        "结束编码并呈现：完成编码并将渲染的图像呈现到屏幕上。",
        "提交命令缓冲区：将命令发送到 GPU 以供执行。"
    ]
  }
];