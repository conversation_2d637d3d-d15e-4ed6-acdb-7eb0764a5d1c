
export interface SimulationStep {
  id: string;
  title: string;
  description: string;
  codeSnippet: string;
  isRenderLoop?: boolean; // Indicates if this step is the continuous render loop
  details?: string[]; // Further details for the process tab
}

export interface LogEntry {
  timestamp: Date;
  message: string;
  type: 'info' | 'code' | 'event' | 'error';
}

export enum Tab {
  PROCESS = '流程步骤',
  CODE = '代码片段',
  LOG = '事件日志',
}