问题分析：

您的动画是基于 CVDisplayLink 和一个 startTime 变量来计算动画时间的。

1.  startTime 的初始化：startTime 变量在 viewDidLoad() 中被初始化为 CACurrentMediaTime() (在 ViewController.swift 的第 136 行)。viewDidLoad() 通常在一个 ViewController 的实例被创建时只调用一次。
2.  动画的暂停与恢复：
    *   当窗口关闭时，viewDidDisappear() 调用 pauseRendering() (在 ViewController.swift 的第 74-80 行)，这会停止 CVDisplayLink (在 ViewController.swift 的第 157-159 行) 并设置 isRenderingActive = false。
    *   当窗口重新打开时，viewWillAppear() 调用 resumeRendering() (在 ViewController.swift 的第 66-72 行)，这会启动 CVDisplayLink (在 ViewController.swift 的第 145-147 行) 并设置 isRenderingActive = true。
3.  动画时间的计算：在 draw(in view: MTKView) 方法中，动画时间 currentTime 是通过 CACurrentMediaTime() - startTime 来计算的 (在 ViewController.swift 的第 398 行)。
4.  导致问题的原因：
    当窗口关闭并重新打开时，如果 ViewController 实例没有被销毁并重新创建（这是常见的行为，视图控制器可能只是隐藏和显示），那么 viewDidLoad() 不会再次调用，startTime 也不会被更新。
    这意味着，当动画在窗口重新打开后恢复时，CACurrentMediaTime() 会是一个比 startTime 大得多的值（因为时间一直在流逝），导致 currentTime 变成一个非常大的数字。虽然理论上大的时间值仍然可以通过 sin 和 cos 函数进行旋转，但实际上，由于浮点精度问题或者旋转速度变得极快（每帧跳过多个整周），人眼可能无法感知到旋转，从而看起来像是动画停止了。

解决方案：

在 resumeRendering() 方法中，当渲染被激活时，重新设置 startTime 为当前的 CACurrentMediaTime()。这样，每次动画恢复时，时间计算都会从一个“新”的起点开始，确保 currentTime 的值是自恢复以来的实际流逝时间，从而保持动画的流畅性。

修改建议：

在 ViewController.swift 文件的 resumeRendering() 方法中添加一行代码来更新 startTime。

当前代码 (部分):
```swift
    private func resumeRendering() {
        guard !isRenderingActive else { return }
        print("恢复 Metal 渲染")
        isRenderingActive = true

        // 启动 CVDisplayLink
        if let displayLink = displayLink {
            CVDisplayLinkStart(displayLink)
            print("CVDisplayLink 启动")
        }
    }
```

建议修改后的代码 (部分):
```swift
    private func resumeRendering() {
        guard !isRenderingActive else { return }
        print("恢复 Metal 渲染")
        isRenderingActive = true
        startTime = CACurrentMediaTime() // <-- 添加这一行

        // 启动 CVDisplayLink
        if let displayLink = displayLink {
            CVDisplayLinkStart(displayLink)
            print("CVDisplayLink 启动")
        }
    }
```

流程说明：

1. 应用启动/ViewController 初始化 -> 2. viewDidLoad?
3. viewDidLoad? (是) -> 4. setupMetalRendering
5. setupMetalRendering -> 6. 初始化 Metal, setupDisplayLink
7. 初始化 Metal, setupDisplayLink -> 8. startTime = CACurrentMediaTime()
9. startTime = CACurrentMediaTime() -> 10. viewWillAppear
11. viewWillAppear -> 12. resumeRendering
13. resumeRendering -> 14. isRenderingActive?
15. isRenderingActive? (否) -> 16. isRenderingActive = true; 设置 startTime = CACurrentMediaTime()
17. isRenderingActive = true; 设置 startTime = CACurrentMediaTime() -> 18. CVDisplayLinkStart
19. CVDisplayLinkStart -> 20. renderFrame 调用 setNeedsDisplay
21. renderFrame 调用 setNeedsDisplay -> 22. drawInMTKView
23. drawInMTKView -> 24. currentTime = CACurrentMediaTime() - startTime
25. currentTime = CACurrentMediaTime() - startTime -> 26. updateUniforms 更新动画
27. updateUniforms 更新动画 -> 28. 渲染地球

29. 渲染地球 -> 30. 用户关闭窗口
31. 用户关闭窗口 -> 32. viewDidDisappear
33. viewDidDisappear -> 34. pauseRendering
35. pauseRendering -> 36. isRenderingActive = false
37. isRenderingActive = false -> 38. CVDisplayLinkStop

39. CVDisplayLinkStop -> 40. 用户重新打开窗口
41. 用户重新打开窗口 -> 10. viewWillAppear