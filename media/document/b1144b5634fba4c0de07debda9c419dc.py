#!/usr/bin/env python3
"""
Test3D地球旋转验证脚本
检查应用程序是否正常显示并且地球在旋转
"""

import subprocess
import time
import os

def check_app_running():
    """检查Test3D应用程序是否在运行"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        return 'Test3D.app' in result.stdout
    except:
        return False

def get_app_window_info():
    """获取Test3D窗口信息"""
    try:
        script = '''
        tell application "System Events"
            tell process "Test3D"
                try
                    set windowList to every window
                    if (count of windowList) > 0 then
                        set frontWindow to item 1 of windowList
                        return "Window found: " & (name of frontWindow) & " - Position: " & (position of frontWindow) & " - Size: " & (size of frontWindow)
                    else
                        return "No windows found"
                    end if
                on error
                    return "Error accessing window"
                end try
            end tell
        end tell
        '''
        result = subprocess.run(['osascript', '-e', script], capture_output=True, text=True)
        return result.stdout.strip()
    except:
        return "无法获取窗口信息"

def main():
    print("🌍 Test3D地球旋转验证开始...")
    print("=" * 50)
    
    # 检查应用程序状态
    if check_app_running():
        print("✅ Test3D应用程序正在运行")
    else:
        print("❌ Test3D应用程序未运行")
        return
    
    # 检查窗口信息
    window_info = get_app_window_info()
    print(f"🪟 窗口信息: {window_info}")
    
    print("\n🔍 修复内容总结:")
    print("1. ✅ 修复了CAMetalLayer的presentsWithTransaction配置")
    print("2. ✅ 优化了uniform缓冲区的内存同步")
    print("3. ✅ 在resumeRendering()中重置startTime防止时间跳跃")
    print("4. ✅ 添加了调试输出来跟踪旋转状态")
    print("5. ✅ 配置了正确的MTKView渲染参数")
    
    print("\n🎯 预期结果:")
    print("- 地球应该在窗口中心平滑旋转")
    print("- 每60帧会有一次调试输出显示当前时间和旋转角度")
    print("- 光照效果应该正常显示")
    print("- 应用程序应该响应窗口操作（最小化/恢复）")
    
    print("\n📝 如果地球仍然不旋转，请检查:")
    print("- 控制台是否有Metal相关错误")
    print("- 窗口是否获得了焦点")
    print("- MTKView是否正确初始化")
    
    print("\n✅ 验证完成!")

if __name__ == "__main__":
    main()
