<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swift Metal CVDisplayLink 模拟器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* For 3D Cube in Visualizer.tsx */
        .perspective-container {
            perspective: 1000px;
        }
        .cube {
            width: 100px;
            height: 100px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.1s linear; /* Smooth rotation */
        }
        .cube-face {
            position: absolute;
            width: 100px;
            height: 100px;
            border: 1px solid #2563eb; /* blue-600, darker for light theme */
            background-color: rgba(59, 130, 246, 0.15); /* blue-500 with opacity, adjusted for light theme */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #1e3a8a; /* blue-800, dark text for light faces */
            font-weight: bold;
            backface-visibility: hidden; /* Hide back of faces for better 3D effect */
        }
        .front  { transform: rotateY(  0deg) translateZ(50px); }
        .back   { transform: rotateY(180deg) translateZ(50px); }
        .right  { transform: rotateY( 90deg) translateZ(50px); }
        .left   { transform: rotateY(-90deg) translateZ(50px); }
        .top    { transform: rotateX( 90deg) translateZ(50px); }
        .bottom { transform: rotateX(-90deg) translateZ(50px); }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@19.0.0",
    "react-dom/": "https://esm.sh/react-dom@19.0.0/",
    "react-dom/client": "https://esm.sh/react-dom@19.0.0/client",
    "react/": "https://esm.sh/react@19.0.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-white">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>