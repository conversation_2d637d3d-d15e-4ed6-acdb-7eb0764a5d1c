
import React from 'react';

export const CodeIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M10.5 4.782a.75.75 0 0 1-.75-.75V2.25a.75.75 0 0 1 1.5 0v1.782a.75.75 0 0 1-.75.75Zm0 14.436a.75.75 0 0 1-.75.75v1.782a.75.75 0 0 1 1.5 0v-1.782a.75.75 0 0 1-.75-.75Zm4.36-9.449L9.187 5.093a.75.75 0 0 1 1.06-1.061l6.574 6.574a.75.75 0 0 1 0 1.06l-6.573 6.574a.75.75 0 0 1-1.061-1.061l5.673-5.673a.75.75 0 0 0 0-1.06Z" clipRule="evenodd" />
    <path fillRule="evenodd" d="M13.5 4.782a.75.75 0 0 0 .75-.75V2.25a.75.75 0 0 0-1.5 0v1.782a.75.75 0 0 0 .75.75Zm0 14.436a.75.75 0 0 0 .75.75v1.782a.75.75 0 0 0-1.5 0v-1.782a.75.75 0 0 0 .75-.75Zm-4.36-9.449 5.673 5.673a.75.75 0 0 0 1.061-1.061L10.25 10.56a.75.75 0 0 0-1.06 0L3.517 16.233a.75.75 0 0 0 1.06 1.061l5.673-5.674a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" />
  </svg>
);
    