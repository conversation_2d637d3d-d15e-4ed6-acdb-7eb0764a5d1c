<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal 动画驱动机制：CVDisplayLink 与 Timer 对比</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #0056b3;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ccc;
            background-color: #eee;
        }
        pre {
            background-color: #e9e9e9;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .pros, .cons {
            margin-left: 20px;
        }
        .pros li { color: #28a745; }
        .cons li { color: #dc3545; }
        .mermaid {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
        }
        .mermaid-animation-controls {
            text-align: center;
            margin-top: 10px;
        }
        .mermaid-animation-controls button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .mermaid-animation-controls button:hover {
            background-color: #0056b3;
        }
        .mermaid-animation-controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Metal 动画驱动机制：CVDisplayLink 与 Timer 对比</h1>
        <p>本文旨在解释 Metal 渲染中动画驱动的两种常见机制：<code>CVDisplayLink</code> 和 <code>Timer</code>，并分析它们各自的优缺点。同时，将结合您的 <code>Test3D</code> 项目，说明其当前动画的实现方式，并提供一个基于时间推移的动画演示。</p>

        <h2>1. <code>CVDisplayLink</code> 与 <code>Timer</code> 的对比分析</h2>
        <p>在 macOS/iOS 平台上，实现流畅的动画和图形渲染，选择合适的帧率驱动机制至关重要。<code>CVDisplayLink</code> 和 <code>Timer</code> 是两种常用的方式，但它们的工作原理和适用场景有显著不同。</p>

        <h3>1.1. <code>CVDisplayLink</code> 的优势</h3>
        <ul class="pros">
            <li><strong>垂直同步 (V-Sync) 和消除画面撕裂 (Tearing)</strong>：
                <p><code>CVDisplayLink</code> 是 CoreVideo 框架提供的一个低层级 API，它能够与显示器的垂直刷新周期精确同步。这意味着它只会在显示器准备好绘制新帧时才触发您的渲染回调。这确保了每一帧都在显示器刷新周期的开始时被完整地绘制和呈现，从而彻底消除了“画面撕裂”现象，提供了极其流畅和专业的视觉体验。</p>
            </li>
            <li><strong>精确的帧率控制和时间同步</strong>：
                <p><code>CVDisplayLink</code> 直接与硬件显示刷新率挂钩（例如 60Hz 或 120Hz）。它提供了一个非常精确和稳定的时间源，确保动画以显示器支持的最高、最稳定的帧率运行。这使得动画的步进更加均匀，避免了因帧率不稳定导致的卡顿。</p>
            </li>
            <li><strong>节能和性能优化</strong>：
                <p>由于 <code>CVDisplayLink</code> 只有在显示器需要新帧时才触发渲染，它避免了不必要的 CPU 和 GPU 运算。如果应用程序的渲染速度远超显示器刷新率，<code>CVDisplayLink</code> 会自动限制渲染，防止资源浪费，从而降低功耗，延长电池续航时间。</p>
            </li>
            <li><strong>降低输入延迟 (Input Latency)</strong>：
                <p>通过与显示器同步，<code>CVDisplayLink</code> 有助于减少从用户输入到屏幕上视觉反馈之间的时间延迟，使得交互式应用程序感觉更加灵敏。</p>
            </li>
            <li><strong>系统级集成和稳定性</strong>：
                <p>作为 Apple 官方提供的底层 API，<code>CVDisplayLink</code> 与 macOS/iOS 的图形系统紧密集成，是一个非常稳定和可靠的机制。</p>
            </li>
        </ul>

        <h3>1.2. <code>Timer</code> 的局限性</h3>
        <ul class="cons">
            <li><strong>无法保证垂直同步</strong>：
                <p><code>Timer</code>（如 <code>NSTimer</code> 或 Swift 的 <code>Timer</code>）是基于 CPU 的，它的触发时间可能会受到 RunLoop 负载、其他任务或系统调度等因素的影响。这意味着 <code>Timer</code> 无法保证在显示器刷新周期的精确时间点触发渲染，从而容易导致画面撕裂。</p>
            </li>
            <li><strong>帧率不稳定</strong>：
                <p>由于系统调度的不确定性，<code>Timer</code> 的回调可能不会严格按照设定的间隔触发，导致实际帧率波动，动画出现不流畅或卡顿。</p>
            </li>
            <li><strong>可能导致资源浪费</strong>：
                <p>如果 <code>Timer</code> 触发频率过高，或者在不需要更新屏幕时也持续触发渲染，可能会导致不必要的 CPU 和 GPU 运算，增加功耗。</p>
            </li>
        </ul>

        <h2>2. <code>Test3D</code> 项目当前动画机制解释</h2>
        <p>在您的 <code>Test3D</code> 项目中，地球的旋转动画是一个“内置动画”，这意味着动画的**逻辑**是您在 <code>ViewController.swift</code> 中自定义编写的，而不是依赖于 Metal 框架提供的某个高级动画组件。</p>
        <p>具体来说，地球的旋转角度是通过 <code>updateUniforms</code> 方法中的以下代码计算的：</p>
        <pre><code>let rotationY = time * 1.0  // 地球自转速度
// 模型矩阵 - 地球自转
let modelMatrix = simd_float4x4(rotationY: rotationY)
</code></pre>
        <p>这里的 <code>time</code> 是从程序启动开始的持续时间，它会不断增长，从而使 <code>rotationY</code> 持续变化，最终导致地球在每一帧都旋转一点点。</p>
        <p>关于动画的**驱动机制**，根据您项目中的代码和日志：</p>
        <pre><code>NSLog("🎬 简化渲染：直接启动备用计时器作为主要渲染机制")
NSLog("🚨 启动永久备用渲染计时器 - 作为主要渲染机制")
</code></pre>
        <p>这表明，尽管代码中存在 <code>CVDisplayLink</code> 的实现，但目前项目**主要通过一个高精度的 <code>Timer</code>（即 <code>backupTimer</code>）来周期性地调用 <code>metalView.draw()</code> 方法，从而驱动渲染循环**，实现地球的连续旋转动画。</p>

        <h2>3. 概念性代码流程：将 Timer 切换为 CVDisplayLink (Swift)</h2>
        <p>如果您希望将 <code>Test3D</code> 项目的渲染驱动机制从 <code>Timer</code> 切换为 <code>CVDisplayLink</code>，以下是关键的修改思路和流程：</p>
        <pre><code>// 1. 在 ViewController 中声明 CVDisplayLink 属性
private var displayLink: CVDisplayLink?

// 2. 修改 resumeRendering 方法
internal func resumeRendering() {
    guard !isRenderingActive else { return }
    NSLog("🔧 恢复 Metal 渲染")
    isRenderingActive = true

    // 停止所有旧的渲染机制 (例如停止 backupTimer)
    stopBackupTimer() // 确保停止 Timer

    // 配置 MTKView 以便由 CVDisplayLink 手动驱动
    metalView.isPaused = true               // 暂停 MTKView 的自动渲染
    metalView.enableSetNeedsDisplay = true  // 启用手动 setNeedsDisplay

    // 启动 CVDisplayLink
    startCVDisplayLink()

    // 保持 startTime 的连续性逻辑 (与现有代码保持一致)
    let currentMediaTime = CACurrentMediaTime()
    if frameCount > 0 {
        startTime = currentMediaTime - (currentMediaTime - startTime)
    } else {
        startTime = currentMediaTime
    }

    NSLog("MTKView 渲染已恢复，由 CVDisplayLink 驱动")
}

// 3. 修改 pauseRendering 方法
internal func pauseRendering() {
    guard isRenderingActive else { return }
    print("暂停 Metal 渲染")
    isRenderingActive = false

    // 停止 CVDisplayLink
    stopCVDisplayLink()

    // 暂停 MTKView 渲染
    metalView.isPaused = true
    
    print("MTKView 渲染已暂停")
}

// 4. 确保 cvDisplayLinkCallback 正确触发渲染
private func cvDisplayLinkCallback() {
    guard isRenderingActive else { return }
    
    DispatchQueue.main.async { [weak self] in
        guard let self = self, self.isRenderingActive else { return }
        
        // 手动触发 MTKView 渲染
        self.metalView.setNeedsDisplay(self.metalView.bounds) // 标记需要重绘
        self.metalView.draw() // 强制立即重绘
        
        if self.frameCount % 60 == 0 {
            NSLog("🔄 CVDisplayLink 强制渲染 Frame \(self.frameCount)")
        }
    }
}

// 5. 移除或禁用 startBackupTimer 和 stopBackupTimer 的调用，因为不再是主要机制
// (保留方法定义，但确保它们不会被 resumeRendering/pauseRendering 调用)
</code></pre>

        <h2>4. JavaScript 动画演示</h2>
        <p>为了直观演示“时间推移动画”的概念，这里使用 HTML5 Canvas 和 JavaScript 的 <code>requestAnimationFrame</code> API 来绘制一个随着时间旋转的方块。<code>requestAnimationFrame</code> 在 Web 浏览器中扮演的角色与 <code>CVDisplayLink</code> 在原生应用中非常相似，都是为了实现与显示器刷新同步的流畅动画。</p>

        <canvas id="animationCanvas" width="400" height="400"></canvas>

        <script>
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');
            let startTime = null; // 动画开始时间

            function draw(currentTime) {
                if (!startTime) {
                    startTime = currentTime;
                }
                const elapsedTime = (currentTime - startTime) / 1000; // 转换为秒

                ctx.clearRect(0, 0, canvas.width, canvas.height); // 清除画布

                ctx.save(); // 保存当前绘图状态

                // 将原点移动到画布中心
                ctx.translate(canvas.width / 2, canvas.height / 2);

                // 根据时间计算旋转角度 (模拟 rotationY = time * 1.0)
                const rotationAngle = elapsedTime * (Math.PI / 2); // 每秒旋转 90 度

                ctx.rotate(rotationAngle); // 旋转画布

                // 绘制方块
                const size = 100;
                ctx.fillStyle = 'blue';
                ctx.fillRect(-size / 2, -size / 2, size, size); // 从中心绘制方块

                ctx.restore(); // 恢复之前保存的绘图状态

                requestAnimationFrame(draw); // 请求下一帧动画
            }

            // 启动动画
            requestAnimationFrame(draw);
        </script>

        <h2>5. Metal 动画渲染流程动画演示</h2>
        <p>以下流程图展示了从窗体启动到 Metal 渲染动画工作的整个过程。点击“播放动画”按钮，可以逐步观察流程的每个环节。</p>
        <div class="mermaid-animation-controls">
            <button id="playAnimation">播放动画</button>
            <button id="resetAnimation" disabled>重置</button>
        </div>
        <div class="mermaid" id="metalFlowchart">
            graph TD
                A["应用启动/窗体显示"] --> B["ViewController.viewWillAppear()"]
                B --> C["调用 resumeRendering()"]
                C --> D["停止旧渲染机制 (如 Timer)"]
                D --> E["配置 MTKView: isPaused=true, enableSetNeedsDisplay=true"]
                E --> F["启动 CVDisplayLink"]
                F -->|"垂直同步信号"| G["CVDisplayLink 回调 (cvDisplayLinkCallback)"]
                G --> H["主线程: metalView.setNeedsDisplay() & metalView.draw()"]
                H --> I["MTKViewDelegate.draw(in view:)"]
                I --> J["计算当前时间 (currentTime)"]
                J --> K["调用 updateUniforms(time: currentTime)"]
                K --> L["计算模型矩阵 (含旋转)"]
                L --> M["更新 Metal Uniform Buffer"]
                M --> N["创建 Metal Command Buffer"]
                N --> O["创建 Render Command Encoder"]
                O --> P["设置渲染管线状态/深度测试"]
                P --> Q["绑定顶点/Uniform/纹理缓冲区"]
                Q --> R["绘制地球 (drawIndexedPrimitives)"]
                R --> S["结束 Render Encoder"]
                S --> T["提交 Command Buffer"]
                T --> U["呈现 Drawable 到屏幕"]
                U -->|"循环渲染"| G

                classDef default fill:#f9f,stroke:#333,stroke-width:2px
        </div>

        <script>
            try {
                mermaid.initialize({ startOnLoad: true });
            } catch (e) {
                console.error("Mermaid initialization error:", e);
            }

            const flowchartDiv = document.getElementById('metalFlowchart');
            const playButton = document.getElementById('playAnimation');
            const resetButton = document.getElementById('resetAnimation');
            const nodes = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U'];
            const delay = 1000; // 1秒延迟

            let animationInterval;
            let currentNodeIndex = 0;

            function findNodeElement(nodeId) {
                // 尝试多种可能的选择器
                const selectors = [
                    `#${nodeId}`,
                    `#flowchart-${nodeId}-0`,
                    `[id*="${nodeId}"]`,
                    `.node-${nodeId}`,
                    `g[id*="${nodeId}"] rect`,
                    `g[id*="${nodeId}"] circle`,
                    `g[id*="${nodeId}"] polygon`
                ];

                for (const selector of selectors) {
                    const element = flowchartDiv.querySelector(selector);
                    if (element) {
                        console.log(`Found node ${nodeId} with selector: ${selector}`);
                        return element;
                    }
                }

                console.log(`Could not find node: ${nodeId}`);
                return null;
            }

            function resetFlowchartStyles() {
                nodes.forEach(nodeId => {
                    const nodeElement = findNodeElement(nodeId);
                    if (nodeElement) {
                        nodeElement.style.fill = '#f9f';
                        nodeElement.style.stroke = '#333';
                        nodeElement.style.strokeWidth = '2px';
                    }
                });
            }

            function animateFlowchart() {
                console.log('Starting animation...');
                playButton.disabled = true;
                resetButton.disabled = false;

                // 先检查DOM结构
                console.log('Flowchart HTML:', flowchartDiv.innerHTML);

                resetFlowchartStyles(); // Reset all styles before starting

                animationInterval = setInterval(() => {
                    if (currentNodeIndex < nodes.length) {
                        const nodeId = nodes[currentNodeIndex];
                        console.log(`Animating node: ${nodeId} (index: ${currentNodeIndex})`);
                        const nodeElement = findNodeElement(nodeId);
                        if (nodeElement) {
                            nodeElement.style.fill = '#a2d2ff'; // Highlight color
                            nodeElement.style.stroke = '#0056b3';
                            nodeElement.style.strokeWidth = '3px';
                            console.log(`Successfully highlighted node: ${nodeId}`);
                        } else {
                            console.log(`Failed to find node: ${nodeId}`);
                        }
                        currentNodeIndex++;
                    } else {
                        // Loop back to the rendering loop (G)
                        const loopNodeId = 'G';
                        const loopNodeElement = findNodeElement(loopNodeId);
                        if (loopNodeElement) {
                            loopNodeElement.style.fill = '#a2d2ff';
                            loopNodeElement.style.stroke = '#0056b3';
                            loopNodeElement.style.strokeWidth = '3px';
                        }
                        // After one full cycle, reset and restart or stop
                        clearInterval(animationInterval);
                        setTimeout(() => {
                            resetFlowchartStyles();
                            currentNodeIndex = 0;
                            playButton.disabled = false; // Allow replaying
                        }, delay * 2); // Give some time to see the last highlight
                    }
                }, delay);
            }

            function stopAnimation() {
                clearInterval(animationInterval);
                playButton.disabled = false;
                resetButton.disabled = true;
                resetFlowchartStyles();
                currentNodeIndex = 0;
            }

            playButton.addEventListener('click', animateFlowchart);
            resetButton.addEventListener('click', stopAnimation);

            // Wait for Mermaid to render before setting up animation
            setTimeout(() => {
                console.log('Mermaid should be rendered now');
                resetFlowchartStyles();
            }, 1000); // Increased delay to ensure Mermaid is fully rendered
        </script>
    </div>
</body>
</html>
