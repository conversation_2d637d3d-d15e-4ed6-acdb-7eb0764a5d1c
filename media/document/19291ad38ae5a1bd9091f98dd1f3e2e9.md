# VS Code MCP 完美配置指南

## 概述

本指南提供了一套完整的VS Code MCP（Model Context Protocol）配置方案，专门优化了Claude、GitHub Copilot等AI助手的工作效率，彻底解决了内置编辑工具缓慢的问题。

## 核心理念

- **优先使用MCP工具**：Desktop Commander > Filesystem > 内置工具
- **禁用缓慢的内置编辑工具**：彻底禁用带有"保留"和"撤消"功能的编辑界面
- **中文优先**：所有AI交互使用中文
- **效率至上**：避免重复操作，一次性完成任务

## 配置的MCP服务器

### 1. Desktop Commander（主力工具）
- **功能**：文件操作、终端控制、代码编辑
- **优势**：速度快、功能全面、与Claude配合完美

### 2. Context7
- **功能**：技术文档查询
- **用途**：获取最新的开发文档和API信息

### 3. Exa
- **功能**：网络搜索
- **用途**：搜索技术资料和解决方案

### 4. Filesystem（备用）
- **功能**：基础文件操作
- **用途**：当Desktop Commander不可用时的备选方案

### 5. Time Server
- **功能**：时间相关操作
- **配置**：洛杉矶时区

## 安装步骤

### 第一步：安装Desktop Commander

```bash
npx @wonderwhy-er/desktop-commander@latest setup
```

这个命令会：
- 自动安装Desktop Commander
- 创建Claude Desktop配置文件
- 配置基本的MCP服务器设置

### 第二步：配置VS Code Settings

使用Python脚本自动配置VS Code settings.json文件。

## 完整配置脚本

创建以下Python脚本来自动配置VS Code：

```python
#!/usr/bin/env python3
import json
import os

def configure_vscode_mcp():
    """完美配置VS Code MCP设置"""
    
    # VS Code settings.json路径
    settings_path = "/Users/<USER>/Library/Application Support/Code/User/settings.json"
    # Windows路径: "%APPDATA%/Code/User/settings.json"
    # Linux路径: "~/.config/Code/User/settings.json"
    
    # 备份原文件
    backup_path = settings_path + ".backup_mcp"
    if os.path.exists(settings_path):
        with open(settings_path, 'r', encoding='utf-8') as f:
            backup_data = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(backup_data)
        print(f"✅ 已备份原配置到: {backup_path}")
    
    # 读取或创建配置
    if os.path.exists(settings_path):
        with open(settings_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        data = {}
    
    # 基础配置
    data.update({
        "workbench.colorTheme": "Visual Studio Light",
        "workbench.startupEditor": "none",
        "git.openRepositoryInParentFolders": "never",
        "git.ignoreLegacyWarning": True,
        "chat.mcp.discovery.enabled": True,
        "github.copilot.chat.localeOverride": "zh-CN",
        "github.copilot.chat.welcomeMessage": "always"
    })
    
    # 禁用GitHub Copilot内置编辑工具（关键配置）
    data.update({
        "github.copilot.editor.enableCodeActions": False,
        "github.copilot.editor.iterativeFixing": False,
        "github.copilot.chat.useProjectTemplates": False,
        "github.copilot.chat.useCodebaseContext": False,
        "github.copilot.advanced": {
            "debug.overrideEngine": "copilot-chat",
            "debug.useElectronNetworking": True,
            "debug.useNodeFetcher": False
        }
    })
    
    # MCP服务器配置
    data["mcp"] = {
        "inputs": [],
        "servers": {
            "mcp-server-time": {
                "command": "python",
                "args": ["-m", "mcp_server_time", "--local-timezone=America/Los_Angeles"],
                "env": {}
            },
            "filesystem": {
                "type": "stdio",
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>"],
                # Windows: "C:\\Users\\<USER>\n请重启VS Code以使配置生效！")

if __name__ == "__main__":
    main()
```

## 使用方法

### 1. 准备工作
- 确保已安装Node.js
- 确保已安装VS Code
- 获取Exa API密钥（如需要）

### 2. 执行配置
```bash
# 1. 安装Desktop Commander
npx @wonderwhy-er/desktop-commander@latest setup

# 2. 创建配置脚本
# 将上面的Python代码保存为 configure_mcp.py

# 3. 修改脚本中的用户名和API密钥
# 替换 {username} 为实际用户名
# 替换 your-exa-api-key-here 为实际的Exa API密钥

# 4. 执行配置
python3 configure_mcp.py

# 5. 重启VS Code
```

### 3. 验证配置
重启VS Code后，检查：
- MCP服务器是否正常连接
- AI助手是否使用desktop-commander而非内置工具
- 中文交互是否正常

## 关键配置说明

### 禁用内置编辑工具的关键设置
```json
{
    "github.copilot.editor.enableCodeActions": false,
    "github.copilot.editor.iterativeFixing": false,
    "github.copilot.chat.useProjectTemplates": false,
    "github.copilot.chat.useCodebaseContext": false
}
```

### MCP工具优先级
1. **Desktop Commander** - 主力工具，处理所有文件操作
2. **Context7** - 技术文档查询
3. **Exa** - 网络搜索
4. **Filesystem** - 备用文件操作工具

## 故障排除

### 常见问题
1. **MCP服务器连接失败**
   - 检查Node.js版本
   - 重新安装MCP服务器
   - 检查网络连接

2. **仍然使用内置编辑工具**
   - 确认VS Code已重启
   - 检查配置文件语法
   - 验证禁用设置是否生效

3. **中文交互异常**
   - 检查locale设置
   - 验证用户指导原则配置

## 维护和更新

### 定期维护
- 更新Desktop Commander到最新版本
- 检查MCP服务器状态
- 备份配置文件

### 配置更新
使用相同的Python脚本方法进行配置更新，确保一致性和可重复性。

## 总结

这套配置方案彻底解决了AI助手使用缓慢内置工具的问题，通过强制使用高效的MCP工具，显著提升了开发效率。所有团队成员都应该按照这个标准进行配置，确保一致的开发体验。

---

**配置完成时间**: 2025年1月
**适用版本**: VS Code 1.85+, Claude Desktop, GitHub Copilot
**维护者**: AI开发团队
