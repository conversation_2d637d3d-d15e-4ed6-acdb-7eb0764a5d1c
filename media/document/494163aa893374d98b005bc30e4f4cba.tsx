
import React from 'react';

export const TerminalIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M2.25 4.5A.75.75 0 0 0 1.5 5.25v13.5c0 .414.336.75.75.75h20.25a.75.75 0 0 0 .75-.75V5.25a.75.75 0 0 0-.75-.75H2.25ZM3 18.75V9.75h18v9H3Zm0-10.5v-2.25h18V8.25H3Z" clipRule="evenodd" />
    <path d="m6.922 11.578-.694.81a.75.75 0 0 0 1.117.996l1.531-1.276a.75.75 0 0 0 0-1.004L7.344 9.828a.75.75 0 0 0-1.117.996l.694.81H3.75a.75.75 0 0 0 0 1.5h3.172Z" />
  </svg>
);
    