
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { SimulationStep, LogEntry, Tab } from './types';
import { SIMULATION_STEPS, INITIAL_LOGS } from './constants';
import SimulationControl from './components/SimulationControl';
import Visualizer from './components/Visualizer';
import InfoPanel from './components/InfoPanel';
import Timeline from './components/Timeline';
import { FilmIcon } from './components/icons/FilmIcon';

const App: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [logs, setLogs] = useState<LogEntry[]>(INITIAL_LOGS);
  const [rotationAngle, setRotationAngle] = useState<number>(0);
  const [frameCount, setFrameCount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<Tab>(Tab.PROCESS);

  const animationFrameId = useRef<number | null>(null);
  const stepTimeoutId = useRef<number | null>(null); // Changed from NodeJS.Timeout

  const currentStep: SimulationStep | undefined = SIMULATION_STEPS[currentStepIndex];

  const addLog = useCallback((message: string, type: 'info' | 'code' | 'event' | 'error' = 'info') => {
    setLogs(prevLogs => [...prevLogs, { timestamp: new Date(), message, type }]);
  }, []);

  const resetSimulation = useCallback(() => {
    setIsRunning(false);
    setCurrentStepIndex(0);
    setLogs(INITIAL_LOGS);
    setRotationAngle(0);
    setFrameCount(0);
    setActiveTab(Tab.PROCESS);
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }
    if (stepTimeoutId.current) {
      clearTimeout(stepTimeoutId.current);
      stepTimeoutId.current = null;
    }
    addLog('模拟已重置。', 'info');
  }, [addLog]);

  // Effect for progressing through setup steps
  useEffect(() => {
    if (isRunning && currentStep && !currentStep.isRenderLoop) {
      addLog(`正在执行: ${currentStep.title}`, 'event');
      stepTimeoutId.current = window.setTimeout(() => { 
        if (currentStepIndex < SIMULATION_STEPS.length - 1) {
          setCurrentStepIndex(prev => prev + 1);
        } else {
           addLog('设置阶段完成。准备进入渲染循环。', 'event');
        }
      }, 1500); 
    }
    return () => {
      if (stepTimeoutId.current) {
        clearTimeout(stepTimeoutId.current);
      }
    };
  }, [isRunning, currentStepIndex, currentStep, addLog]);

  // Effect for render loop simulation
  const renderLoop = useCallback(() => {
    setRotationAngle(prev => (prev + 1) % 360);
    setFrameCount(prevFc => {
        const newFrameCount = prevFc + 1;
        if (newFrameCount % 60 === 0) { 
            addLog(`渲染循环: 帧 ${newFrameCount}, 角度 ${(rotationAngle + 1) % 360}°`, 'event');
        }
        return newFrameCount;
    });
    
    animationFrameId.current = requestAnimationFrame(renderLoop);
  }, [addLog, rotationAngle]);

  useEffect(() => {
    const renderLoopStep = SIMULATION_STEPS.find(step => step.isRenderLoop);
    if (isRunning && renderLoopStep && currentStepIndex >= SIMULATION_STEPS.indexOf(renderLoopStep)) {
      if (!animationFrameId.current) {
        addLog('CVDisplayLink 已激活：开始渲染循环模拟。', 'event');
        animationFrameId.current = requestAnimationFrame(renderLoop);
      }
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
        animationFrameId.current = null;
         if(isRunning && renderLoopStep && currentStepIndex < SIMULATION_STEPS.indexOf(renderLoopStep)) {
           // This case means we are running but not yet at render loop step.
         } else if (!isRunning && renderLoopStep && currentStepIndex >= SIMULATION_STEPS.indexOf(renderLoopStep)) {
           addLog('渲染循环已暂停（由于模拟暂停）。', 'event');
         }
      }
    }
    
    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
        animationFrameId.current = null;
      }
    };
  }, [isRunning, currentStepIndex, renderLoop, addLog]);


  const handleTogglePlay = () => {
    const newIsRunning = !isRunning;
    setIsRunning(newIsRunning);

    if (newIsRunning) {
      if (currentStepIndex === 0 && logs.length <= INITIAL_LOGS.length) { 
        addLog('模拟已启动。', 'event');
      } else {
        const renderLoopStep = SIMULATION_STEPS.find(step => step.isRenderLoop);
        const isAtRenderLoop = renderLoopStep && currentStepIndex >= SIMULATION_STEPS.indexOf(renderLoopStep);
        addLog(isAtRenderLoop ? '渲染循环已恢复。' : '模拟已恢复。', 'event');
      }
    } else {
      const renderLoopStep = SIMULATION_STEPS.find(step => step.isRenderLoop);
      const wasAtRenderLoop = renderLoopStep && currentStepIndex >= SIMULATION_STEPS.indexOf(renderLoopStep);
      if (!wasAtRenderLoop) {
         addLog('模拟已暂停。', 'event');
      }
      if (stepTimeoutId.current) {
        clearTimeout(stepTimeoutId.current);
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col p-4 md:p-8 bg-gradient-to-br from-gray-50 to-slate-100 text-gray-800">
      <header className="mb-6 text-center">
        <div className="flex items-center justify-center space-x-3">
          <FilmIcon className="h-10 w-10 text-sky-500" />
          <h1 className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-500 to-blue-600">
            Swift Metal CVDisplayLink 模拟器
          </h1>
        </div>
        <p className="text-gray-600 mt-2 text-sm md:text-base">
          可视化使用 Metal 和 CVDisplayLink 的 3D 动画管线。
        </p>
      </header>

      <Timeline steps={SIMULATION_STEPS} currentStepIndex={currentStepIndex} />

      <SimulationControl
        isRunning={isRunning}
        onTogglePlay={handleTogglePlay}
        onReset={resetSimulation}
      />

      <main className="flex-grow grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <Visualizer 
            currentStep={currentStep} 
            rotationAngle={rotationAngle} 
            frameCount={frameCount} 
            isRunning={isRunning && currentStep?.isRenderLoop === true}
        />
        <InfoPanel
          currentStep={currentStep}
          logs={logs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </main>

      <footer className="text-center mt-8 text-xs text-gray-500">
        <p>模拟环境。不执行任何实际的 Swift/Metal 代码。</p>
        <p>&copy; {new Date().getFullYear()} AI 生成的 React 应用</p>
      </footer>
    </div>
  );
};

export default App;