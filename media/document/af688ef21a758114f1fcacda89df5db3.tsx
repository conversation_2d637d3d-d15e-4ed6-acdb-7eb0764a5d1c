
import React from 'react';
import { PlayIcon } from './icons/PlayIcon';
import { PauseIcon } from './icons/PauseIcon';
import { ResetIcon } from './icons/ResetIcon';

interface SimulationControlProps {
  isRunning: boolean;
  onTogglePlay: () => void;
  onReset: () => void;
}

const SimulationControl: React.FC<SimulationControlProps> = ({ isRunning, onTogglePlay, onReset }) => {
  return (
    <div className="my-6 flex justify-center items-center space-x-4 p-4 bg-slate-100 rounded-lg shadow-xl">
      <button
        onClick={onTogglePlay}
        className={`px-6 py-3 rounded-md font-semibold text-white transition-all duration-150 ease-in-out
                    flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-opacity-70
                    ${isRunning 
                        ? 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500' 
                        : 'bg-green-500 hover:bg-green-600 focus:ring-green-500'}`}
        aria-label={isRunning ? '暂停模拟' : '开始模拟'}
      >
        {isRunning ? <PauseIcon className="h-5 w-5" /> : <PlayIcon className="h-5 w-5" />}
        <span>{isRunning ? '暂停' : '开始'}</span>
      </button>
      <button
        onClick={onReset}
        className="px-6 py-3 rounded-md font-semibold text-white bg-red-500 hover:bg-red-600 transition-all duration-150 ease-in-out
                   flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-70"
        aria-label="重置模拟"
      >
        <ResetIcon className="h-5 w-5" />
        <span>重置</span>
      </button>
    </div>
  );
};

export default SimulationControl;