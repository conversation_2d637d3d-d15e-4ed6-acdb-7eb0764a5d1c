
import React from 'react';
import { SimulationStep } from '../types';
import { DesktopIcon } from './icons/DesktopIcon';
import { CogIcon } from './icons/CogIcon';
import { SIMULATION_STEPS } from '../constants'; // Import SIMULATION_STEPS

interface VisualizerProps {
  currentStep?: SimulationStep;
  rotationAngle: number;
  frameCount: number;
  isRunning: boolean; // Specifically for render loop activity
}

const Visualizer: React.FC<VisualizerProps> = ({ currentStep, rotationAngle, frameCount, isRunning }) => {
  const appLaunchIndex = SIMULATION_STEPS.findIndex(s => s.id === 'app_launch');
  const metalViewSetupIndex = SIMULATION_STEPS.findIndex(s => s.id === 'metal_view_setup');
  const rendererInitIndex = SIMULATION_STEPS.findIndex(s => s.id === 'renderer_init');
  const displayLinkInitIndex = SIMULATION_STEPS.findIndex(s => s.id === 'cvdisplaylink_init');
  
  const currentStepActualIndex = currentStep ? SIMULATION_STEPS.findIndex(s => s.id === currentStep.id) : -1;

  const showWindow = currentStep && currentStepActualIndex >= appLaunchIndex;
  const showMetalView = currentStep && currentStepActualIndex >= metalViewSetupIndex;
  const rendererActive = currentStep && currentStepActualIndex >= rendererInitIndex;
  const displayLinkActive = currentStep && currentStepActualIndex >= displayLinkInitIndex;
  
  return (
    <div className="bg-slate-100 p-4 sm:p-6 rounded-xl shadow-2xl h-full flex flex-col items-center justify-center border border-slate-300 min-h-[400px] md:min-h-[500px]">
      <div className="text-center mb-4">
          <DesktopIcon className="h-8 w-8 text-sky-500 mx-auto mb-1" />
          <h2 className="text-xl font-semibold text-sky-600">可视化模拟</h2>
      </div>
      
      <div className={`w-full max-w-md h-72 md:h-96 bg-gray-200 rounded-lg shadow-lg overflow-hidden transition-opacity duration-500 ${showWindow ? 'opacity-100' : 'opacity-0'}`}>
        <div className="h-8 bg-slate-300 flex items-center px-3 space-x-2 border-b border-slate-400">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-xs text-gray-700 ml-2">Metal 3D 应用</span>
        </div>

        <div className={`w-full h-[calc(100%-2rem)] bg-gray-100 flex items-center justify-center p-4 transition-colors duration-500 ${showMetalView ? 'bg-slate-800/5' : 'bg-gray-100'}`}>
          {showMetalView && (
            <div className="perspective-container w-full h-full flex items-center justify-center">
              <div 
                className="cube" 
                style={{ transform: `rotateX(${rotationAngle * 0.7}deg) rotateY(${rotationAngle}deg) rotateZ(${rotationAngle * 0.3}deg)`}}
              >
                <div className="cube-face front">前</div>
                <div className="cube-face back">后</div>
                <div className="cube-face right">右</div>
                <div className="cube-face left">左</div>
                <div className="cube-face top">上</div>
                <div className="cube-face bottom">下</div>
              </div>
            </div>
          )}
          {!showMetalView && (
            <div className="text-gray-500 text-sm">窗口内容区域</div>
          )}
        </div>
      </div>
      
      <div className="mt-4 grid grid-cols-2 gap-2 text-xs w-full max-w-md">
        <div className={`p-2 rounded ${rendererActive ? 'bg-green-500/20 text-green-700' : 'bg-gray-400/20 text-gray-600'}`}>
            渲染器: {rendererActive ? '活动' : '非活动'}
        </div>
        <div className={`p-2 rounded ${displayLinkActive ? 'bg-sky-500/20 text-sky-700' : 'bg-gray-400/20 text-gray-600'}`}>
            CVDisplayLink: {displayLinkActive ? (isRunning && currentStep?.isRenderLoop ? `运行中 (帧 ${frameCount})` : '已初始化') : '非活动'}
        </div>
      </div>
      {isRunning && currentStep?.isRenderLoop && (
        <div className="mt-2 text-xs text-center text-purple-600 flex items-center justify-center">
            <CogIcon className="h-4 w-4 mr-1 animate-spin" />
            正在渲染第 {frameCount} 帧，角度 {rotationAngle.toFixed(0)}°
        </div>
      )}
       {!currentStep && (
         <p className="mt-4 text-gray-500">模拟尚未开始。</p>
       )}
    </div>
  );
};

export default Visualizer;