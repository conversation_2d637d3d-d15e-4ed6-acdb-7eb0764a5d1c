
import React from 'react';
import { SimulationStep } from '../types';

interface TimelineProps {
  steps: SimulationStep[];
  currentStepIndex: number;
}

const Timeline: React.FC<TimelineProps> = ({ steps, currentStepIndex }) => {
  return (
    <div className="w-full max-w-4xl mx-auto my-4 p-2 md:p-4 bg-slate-100 rounded-lg shadow-lg">
      <div className="flex items-center justify-between space-x-1 md:space-x-2 overflow-x-auto">
        {steps.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;
          return (
            <div key={step.id} className="flex flex-col items-center flex-shrink-0 min-w-[80px] md:min-w-[120px]">
              <div
                className={`w-5 h-5 md:w-6 md:h-6 rounded-full flex items-center justify-center border-2
                            transition-all duration-300
                            ${isActive ? 'bg-sky-500 border-sky-400 scale-110' : isCompleted ? 'bg-green-500 border-green-400' : 'bg-gray-300 border-gray-400'}`}
              >
                {isCompleted && !isActive && <span className="text-white text-xs">✓</span>}
                {isActive && <span className="w-2 h-2 bg-sky-100 rounded-full animate-pulse"></span>}
              </div>
              <p className={`mt-1 text-center text-[10px] md:text-xs font-medium transition-colors duration-300
                           ${isActive ? 'text-sky-600' : isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                {step.title.split('.')[0]} {/* Show only number */}
              </p>
            </div>
          );
        })}
      </div>
      {steps[currentStepIndex] && (
        <p className="text-center text-xs md:text-sm text-gray-700 mt-2">
          当前: <span className="font-semibold text-sky-600">{steps[currentStepIndex].title}</span>
        </p>
      )}
    </div>
  );
};

export default Timeline;