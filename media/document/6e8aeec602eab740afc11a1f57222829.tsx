
import React from 'react';
import { SimulationStep, LogEntry, Tab } from '../types';
import { CodeIcon } from './icons/CodeIcon';
import { TerminalIcon } from './icons/TerminalIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';

interface InfoPanelProps {
  currentStep?: SimulationStep;
  logs: LogEntry[];
  activeTab: Tab;
  onTabChange: (tab: Tab) => void;
}

interface TabButtonProps {
  tab: Tab;
  icon: React.ReactNode;
  children: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
}

const TabButtonComponent: React.FC<TabButtonProps> = ({ tab, icon, children, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={`flex-1 py-2 px-1 sm:px-3 text-xs sm:text-sm font-medium border-b-2 transition-colors duration-150
                flex items-center justify-center space-x-1
                ${isActive ? 'border-sky-500 text-sky-600' : 'border-transparent text-gray-500 hover:text-gray-800 hover:border-gray-400'}`}
    aria-pressed={isActive}
    role="tab"
    aria-controls={`tabpanel-${tab.toLowerCase().replace(/\s+/g, '-')}`}
    id={`tab-${tab.toLowerCase().replace(/\s+/g, '-')}`}
  >
    {icon}
    <span>{children}</span>
  </button>
);


const InfoPanel: React.FC<InfoPanelProps> = ({ currentStep, logs, activeTab, onTabChange }) => {
  return (
    <div className="bg-slate-100 p-0.5 sm:p-1 rounded-xl shadow-2xl flex flex-col h-full border border-slate-300 min-h-[400px] md:min-h-[500px]">
      <div className="flex border-b border-slate-300 bg-slate-100/50 rounded-t-lg" role="tablist">
        <TabButtonComponent tab={Tab.PROCESS} icon={<CheckCircleIcon className="h-4 w-4" />} isActive={activeTab === Tab.PROCESS} onClick={() => onTabChange(Tab.PROCESS)}>
          {Tab.PROCESS}
        </TabButtonComponent>
        <TabButtonComponent tab={Tab.CODE} icon={<CodeIcon className="h-4 w-4" />} isActive={activeTab === Tab.CODE} onClick={() => onTabChange(Tab.CODE)}>
          {Tab.CODE}
        </TabButtonComponent>
        <TabButtonComponent tab={Tab.LOG} icon={<TerminalIcon className="h-4 w-4" />} isActive={activeTab === Tab.LOG} onClick={() => onTabChange(Tab.LOG)}>
          {Tab.LOG}
        </TabButtonComponent>
      </div>

      <div className="flex-grow p-3 sm:p-4 overflow-y-auto bg-white rounded-b-lg">
        {activeTab === Tab.PROCESS && (
          <div role="tabpanel" id="tabpanel-process-steps" aria-labelledby="tab-process-steps" className="text-gray-700 space-y-3">
            {currentStep ? (
              <>
                <h3 className="text-lg font-semibold text-sky-600">{currentStep.title}</h3>
                <p className="text-sm text-gray-600">{currentStep.description}</p>
                {currentStep.details && currentStep.details.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-slate-300">
                        <h4 className="text-sm font-semibold text-sky-700 mb-1">关键操作:</h4>
                        <ul className="list-disc list-inside space-y-1 text-xs text-gray-600">
                            {currentStep.details.map((detail, index) => (
                                <li key={index}>{detail}</li>
                            ))}
                        </ul>
                    </div>
                )}
              </>
            ) : (
              <p className="text-gray-500">未选择步骤。启动模拟以查看详情。</p>
            )}
          </div>
        )}

        {activeTab === Tab.CODE && (
          <div role="tabpanel" id="tabpanel-code-snippet" aria-labelledby="tab-code-snippet">
            {currentStep ? (
              <>
                <h3 className="text-sm font-semibold text-sky-600 mb-2">代码片段: {currentStep.title}</h3>
                <pre className="bg-slate-900 p-3 rounded-md text-xs overflow-x-auto border border-slate-700 whitespace-pre-wrap text-gray-200">
                  <code>{currentStep.codeSnippet.trim()}</code>
                </pre>
              </>
            ) : (
              <p className="text-gray-500">未选择步骤。启动模拟以查看代码片段。</p>
            )}
          </div>
        )}

        {activeTab === Tab.LOG && (
          <div role="tabpanel" id="tabpanel-event-log" aria-labelledby="tab-event-log" className="text-gray-700 text-xs space-y-1.5 font-mono h-[300px] md:h-auto overflow-y-auto">
            {logs.length > 0 ? logs.slice().reverse().map((log, index) => (
              <div key={index} className={`flex items-start ${log.type === 'error' ? 'text-red-600' : log.type === 'event' ? 'text-yellow-600' : log.type === 'code' ? 'text-blue-600' : 'text-gray-600'}`}>
                <span className="text-gray-500 mr-2 select-none">{log.timestamp.toLocaleTimeString('zh-CN', { hour12: false })}</span>
                <span className="flex-1">{log.message}</span>
              </div>
            )) : <p className="text-gray-500">暂无日志。</p>}
          </div>
        )}
      </div>
    </div>
  );
};

export default InfoPanel;