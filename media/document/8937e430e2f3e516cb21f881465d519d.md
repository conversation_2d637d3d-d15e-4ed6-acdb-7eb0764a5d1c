# 模拟器动画连续性增强建议

## 当前模拟器的局限性

现有的HTML模拟器虽然展示了CVDisplayLink的基本工作流程，但**缺少Test3D项目中最核心的动画连续性机制**。

### 缺失的关键功能：

1. **时间连续性算法**：当前只是简单的角度递增，没有基于真实时间的计算
2. **窗口状态恢复**：缺少模拟窗口关闭/重开时的状态保持
3. **frameCount生命周期管理**：没有展示帧计数如何在应用生命周期中保持

## 建议的增强方案

### 1. 添加真实时间基准的动画计算

```javascript
// 在App.tsx中添加
const [startTime, setStartTime] = useState(Date.now());
const [pausedTime, setPausedTime] = useState(0);

const renderLoop = useCallback(() => {
    const currentTime = Date.now();
    const elapsedTime = (currentTime - startTime - pausedTime) / 1000; // 秒
    const rotationInDegrees = (elapsedTime * 60) % 360; // 每秒60度
    
    setRotationAngle(rotationInDegrees);
    setFrameCount(prevFc => prevFc + 1);
}, [startTime, pausedTime]);
```

### 2. 添加窗口状态模拟

```javascript
// 模拟窗口关闭/重开
const [windowVisible, setWindowVisible] = useState(true);
const [lastPauseTime, setLastPauseTime] = useState(0);

const simulateWindowClose = () => {
    setWindowVisible(false);
    setLastPauseTime(Date.now());
    setIsRunning(false);
};

const simulateWindowReopen = () => {
    setWindowVisible(true);
    // 关键：调整startTime以保持连续性
    const pauseDuration = Date.now() - lastPauseTime;
    setPausedTime(prev => prev + pauseDuration);
    setIsRunning(true);
};
```

### 3. 添加时间连续性验证界面

在模拟器中添加一个专门的面板，展示：
- 当前旋转角度的计算过程
- startTime的调整历史
- frameCount的累积过程
- 暂停/恢复时间点的记录

### 4. 添加"窗口操作"按钮

在现有的播放/暂停/重置按钮基础上，添加：
- "模拟关闭窗口" 按钮
- "模拟重新打开" 按钮
- "显示时间连续性日志" 开关

## 实现优先级

1. **高优先级**：添加基于真实时间的旋转计算
2. **中优先级**：添加窗口状态模拟和连续性算法
3. **低优先级**：添加详细的调试界面和日志展示

这样的增强将使模拟器真正反映Test3D项目的核心技术特性。